#!/usr/bin/env bash

# in the case you need to load specific modules on the cluster, add them here
module load eth_proxy

# create job script with compute demands
cat <<EOT > job.sh
#!/bin/bash

#SBATCH -n 1                        # Number of tasks
#SBATCH --cpus-per-task=16         # 16 cores to handle GPU I/O
#SBATCH --gpus=a100_80gb:1         # Request 1 A100 80GB - optimal for 32K environments
#SBATCH --time=23:00:00
#SBATCH --mem-per-cpu=16384        # 16GB per CPU (256GB total)
#SBATCH --mail-type=END
#SBATCH --mail-user=name@mail
#SBATCH --job-name="training-$(date +"%Y-%m-%dT%H:%M")"

# Set environment variables for GPU optimization
export CUDA_VISIBLE_DEVICES=0
export PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:1024
export CUDA_LAUNCH_BLOCKING=0
export TORCH_CUDA_ARCH_LIST="8.0"  # A100 compute capability

# Pass the container profile first to run_singularity.sh, then all arguments intended for the executed script
bash "$1/docker/cluster/run_singularity.sh" "$1" "$2" "${@:3}"
EOT

sbatch < job.sh
rm job.sh